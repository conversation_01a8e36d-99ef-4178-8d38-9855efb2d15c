defmodule Drops.OperationsTest do
  use Drops.OperationCase, async: true

  describe "defining shared functions" do
    test "imports functions from a base module" do
      defmodule Test.Operations do
        use Drops.Operations, type: :command

        def build_user do
          %{name: "<PERSON>"}
        end
      end

      defmodule Test.CreateUser do
        use Test.Operations

        @impl true
        def execute(%{params: _params}) do
          {:ok, build_user()}
        end
      end

      {:ok, result} = Test.CreateUser.call(%{params: %{}})
      assert result == %{name: "<PERSON>"}
    end
  end

  describe "basic operations" do
    operation :command do
      @impl true
      def execute(%{params: params}) do
        if params[:name] == nil do
          {:error, "name is required"}
        else
          {:ok, params}
        end
      end
    end

    test "it works without schema", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{name: "<PERSON>"}})

      assert result == %{name: "<PERSON>"}
    end
  end

  describe "operations with schema" do
    operation :command do
      schema do
        %{
          required(:name) => string(:filled?)
        }
      end

      @impl true
      def execute(%{params: params}) do
        if params[:name] != "<PERSON>" do
          {:error, "name is not expected"}
        else
          {:ok, params}
        end
      end
    end

    test "it works with a schema", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{name: "Jane Do<PERSON>"}})

      assert result == %{name: "Jane Doe"}

      {:error, result} = operation.call(%{params: %{name: ""}})

      assert_errors(["name must be filled"], {:error, result})
    end
  end

  describe "using prepare/1" do
    operation :command do
      schema do
        %{
          required(:name) => string(:filled?),
          required(:template) => boolean()
        }
      end

      @impl true
      def prepare(%{params: %{template: true} = params} = context) do
        updated_params = Map.put(params, :name, params.name <> ".template")
        {:ok, Map.put(context, :params, updated_params)}
      end

      @impl true
      def execute(%{params: params}) do
        {:ok, params}
      end
    end

    test "passes prepared params to execute", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{name: "README.md", template: true}})

      assert result == %{name: "README.md.template", template: true}
    end
  end

  describe "composing multiple operations" do
    operation name: :create_user, type: :command do
      schema do
        %{
          required(:name) => string(:filled?)
        }
      end

      @impl true
      def execute(%{params: params}) do
        {:ok, Map.merge(params, %{id: :rand.uniform(1000)})}
      end
    end

    operation name: :update_user, type: :command do
      schema do
        %{
          required(:name) => string(:filled?)
        }
      end

      @impl true
      def execute(%{execute_result: user, params: params}) do
        {:ok, Map.merge(user, params)}
      end
    end

    test "can safely compose multiple operations", %{
      create_user: create_op,
      update_user: update_op
    } do
      result =
        create_op.call(%{params: %{name: "Jane"}})
        |> update_op.call(%{params: %{name: "Jane Doe"}})

      # Check the structure and that the ID is preserved from the first operation
      assert {:ok, %{id: id, name: "Jane Doe"}} = result

      assert is_integer(id) and id > 0

      result =
        create_op.call(%{params: %{name: ""}})
        |> update_op.call(%{params: %{name: "Jane Doe"}})

      assert {:error, _error} = result

      result =
        create_op.call(%{params: %{name: "Jane"}})
        |> update_op.call(%{params: %{name: ""}})

      assert {:error, _error} = result
    end
  end

  describe "step macro with pattern matching fallbacks" do
    operation type: :command do
      schema do
        %{
          required(:name) => string(),
          required(:email) => string(),
          optional(:role) => string()
        }
      end

      # Custom validate implementation for specific patterns
      step :validate, %{params: %{name: "admin"}} do
        {:error, "Admin user creation not allowed"}
      end

      # Custom prepare implementation with guards
      step :prepare, %{params: %{email: email}} = _context, byte_size(email) > 50 do
        {:error, "Email too long"}
      end

      # Custom prepare for VIP users
      step :prepare, %{params: %{role: "VIP"}} = context do
        {:ok, Map.put(context, :vip_processed, true)}
      end

      def execute(%{params: params} = context) do
        result = Map.take(params, [:name, :email])

        result =
          if Map.get(context, :vip_processed),
            do: Map.put(result, :vip, true),
            else: result

        {:ok, result}
      end
    end

    test "uses custom validate for matching patterns", %{operation: operation} do
      {:error, error} =
        operation.call(%{params: %{name: "admin", email: "<EMAIL>"}})

      assert error == "Admin user creation not allowed"
    end

    test "falls back to default validate for non-matching patterns", %{
      operation: operation
    } do
      {:ok, result} =
        operation.call(%{params: %{name: "user", email: "<EMAIL>"}})

      assert result == %{name: "user", email: "<EMAIL>"}
    end

    test "uses custom prepare with guards", %{operation: operation} do
      long_email = String.duplicate("a", 45) <> "@example.com"
      {:error, error} = operation.call(%{params: %{name: "user", email: long_email}})
      assert error == "Email too long"
    end

    test "uses custom prepare for VIP users", %{operation: operation} do
      {:ok, result} =
        operation.call(%{params: %{name: "vip", email: "<EMAIL>", role: "VIP"}})

      assert result == %{name: "vip", email: "<EMAIL>", vip: true}
    end

    test "falls back to default prepare for regular users", %{operation: operation} do
      {:ok, result} =
        operation.call(%{params: %{name: "user", email: "<EMAIL>", role: "user"}})

      assert result == %{name: "user", email: "<EMAIL>"}
    end
  end
end
