defmodule Drops.Operations.Extensions.Ecto.Behaviour do
  @moduledoc """
  Behaviour for Operations that use Ecto schemas.

  This behaviour defines callbacks specific to Ecto operations, allowing
  operations to use `@impl true` for Ecto-specific functionality.

  ## Callbacks

  - `changeset/1` - Creates an Ecto changeset from context
  - `validate_changeset/1` - Validates an Ecto changeset
  - `get_struct/1` - Gets the struct to use for changeset creation

  ## Example

      operation type: :command do
        schema(MyApp.User)

        @impl true
        def changeset(context) do
          # Custom changeset logic
        end

        @impl true
        def validate_changeset(%{changeset: changeset}) do
          changeset
          |> validate_required([:name, :email])
          |> validate_format(:email, ~r/@/)
        end

        @impl true
        def execute(%{changeset: changeset}) do
          case persist(changeset) do
            {:ok, user} -> {:ok, %{name: user.name}}
            {:error, changeset} -> {:error, changeset}
          end
        end
      end
  """

  @doc """
  Callback for creating an Ecto changeset from context.
  Receives context map and should return {:ok, context_with_changeset} or error tuple.
  """
  @callback changeset(context :: map()) :: {:ok, map()} | {:error, any()}

  @doc """
  Callback for validating an Ecto changeset.
  Receives context map with changeset and should return the validated changeset.
  """
  @callback validate_changeset(context :: map()) :: Ecto.Changeset.t()

  @doc """
  Callback for getting the struct to use for changeset creation.
  Receives context map and should return {:ok, struct} or error tuple.
  """
  @callback get_struct(context :: map()) :: {:ok, struct()} | {:error, any()}

  @optional_callbacks changeset: 1, validate_changeset: 1, get_struct: 1

  @doc """
  Macro for defining Ecto callback implementations with pattern matching fallbacks.

  Similar to Contract's rule macro, this allows defining specific pattern-matched
  implementations while keeping default implementations available for unmatched patterns.
  """
  defmacro ecto_callback(callback_name, pattern, do: block) do
    quote do
      @impl true
      def unquote(callback_name)(unquote(pattern)), do: unquote(block)
    end
  end

  defmacro ecto_callback(callback_name, pattern, guards, do: block) do
    quote do
      @impl true
      def unquote(callback_name)(unquote(pattern)) when unquote(guards),
        do: unquote(block)
    end
  end
end

defmodule Drops.Operations.Extensions.Ecto do
  @behaviour Drops.Operations.Extension

  @moduledoc """
  Ecto extension for Operations.

  This extension adds Ecto-specific functionality to Operations modules when
  a repo is configured. It provides:

  - Changeset validation pipeline
  - `changeset/1` and `persist/1` functions
  - Phoenix.HTML.FormData protocol support for Success/Failure structs
  - Schema error conversion to changeset errors
  - Automatic casting support for Ecto schemas (cast: true by default)
  - Simplified changeset creation leveraging Drops schema casting

  The extension is automatically enabled when the `:repo` option is provided.

  ## Automatic Casting

  When using Ecto schemas in operations, the extension automatically enables
  casting support (`cast: true`) by default. This means that string inputs
  will be automatically cast to the appropriate Ecto types (e.g., "42" to 42
  for integer fields). You can still override this by explicitly setting
  `cast: false` in your schema options.

  ## Examples

      # Automatic casting enabled by default
      operation type: :command do
        schema(MyApp.User)  # cast: true is applied automatically

        def execute(context), do: {:ok, context}
      end

      # Explicitly disable casting if needed
      operation type: :command do
        schema(MyApp.User, cast: false)

        def execute(context), do: {:ok, context}
      end
  """

  @impl true
  def enabled?(opts) do
    Keyword.has_key?(opts, :repo) && !is_nil(opts[:repo])
  end

  @impl true
  def extend_operation(opts) do
    quote location: :keep do
      @behaviour Drops.Operations.Extensions.Ecto.Behaviour

      import Ecto.Changeset

      import Drops.Operations.Extensions.Ecto.Behaviour,
        only: [ecto_callback: 3, ecto_callback: 4]

      # Set default schema options to enable casting for Ecto schemas
      @schema_opts Keyword.merge(Module.get_attribute(__MODULE__, :schema_opts, []),
                     cast: true
                   )

      def ecto_schema, do: schema().meta[:source_schema]
      def __repo__, do: unquote(opts[:repo])

      @before_compile Drops.Operations.Extensions.Ecto

      def validate(%{changeset: changeset} = context) do
        case validate_changeset(%{context | changeset: %{changeset | action: :validate}}) do
          %{valid?: true} = changeset ->
            {:ok, %{context | changeset: %{changeset | action: nil}}}

          changeset ->
            {:error, changeset}
        end
      end

      def persist(changeset) do
        __repo__().insert(%{changeset | action: nil})
      end

      defp cast_embedded_fields(changeset, embedded_fields, params) do
        Enum.reduce(embedded_fields, changeset, fn field, acc ->
          if Map.has_key?(params, field) do
            cast_embed(acc, field)
          else
            acc
          end
        end)
      end
    end
  end

  @doc false
  defmacro __before_compile__(_env) do
    quote do
      # Default fallback implementations for Ecto callbacks
      # These will only be called if no user-defined clauses match

      def get_struct(_context) do
        {:ok, struct(ecto_schema())}
      end

      def changeset(%{params: params} = context) do
        case get_struct(context) do
          {:ok, struct} ->
            schema_module = ecto_schema()
            embedded_fields = schema_module.__schema__(:embeds)

            changeset = change(struct, params)
            changeset = cast_embedded_fields(changeset, embedded_fields, params)

            {:ok, Map.put(context, :changeset, changeset)}

          {:error, _} = error ->
            error
        end
      end

      def validate_changeset(%{changeset: changeset}) do
        changeset
      end
    end
  end

  @impl true
  def extend_unit_of_work(uow, _mod, opts) do
    schema_meta = Keyword.get(opts, :schema_meta, %{})
    default_schema_meta = Map.get(schema_meta, :default, %{})
    has_ecto_schema = Map.get(default_schema_meta, :ecto_schema, false)

    if has_ecto_schema do
      uow |> Drops.Operations.UnitOfWork.after_step(:prepare, :changeset)
    else
      uow
    end
  end

  # Helper function to check if operation has an Ecto schema
  def has_ecto_schema?(operation_module) do
    schema = operation_module.schema()
    !is_nil(schema.meta[:source_schema])
  end

  # Helper function to convert schema validation errors to changeset for form operations
  def convert_schema_errors_to_changeset(operation_module, params, errors) do
    # Convert string keys to atom keys for Ecto changeset compatibility
    atom_params = atomize_keys(params || %{})

    # Create an empty changeset and add the schema errors to it
    context = operation_module.changeset(%{params: atom_params})
    changeset = Map.get(context, :changeset)

    Enum.reduce(errors, changeset, fn error, acc ->
      case error do
        # Handle Drops.Validator.Messages.Error.Type format
        %Drops.Validator.Messages.Error.Type{path: [field], text: text}
        when is_atom(field) ->
          Ecto.Changeset.add_error(acc, field, text)

        %Drops.Validator.Messages.Error.Type{path: [field], text: text}
        when is_binary(field) ->
          field_atom = String.to_existing_atom(field)
          Ecto.Changeset.add_error(acc, field_atom, text)

        # Handle nested paths by flattening to the first level for now
        %Drops.Validator.Messages.Error.Type{path: [field | _], text: text}
        when is_atom(field) ->
          Ecto.Changeset.add_error(acc, field, text)

        %Drops.Validator.Messages.Error.Type{path: [field | _], text: text}
        when is_binary(field) ->
          field_atom = String.to_existing_atom(field)
          Ecto.Changeset.add_error(acc, field_atom, text)

        # Handle generic error format with path and text
        %{path: [field], text: text} when is_atom(field) ->
          Ecto.Changeset.add_error(acc, field, text)

        %{path: [field], text: text} when is_binary(field) ->
          field_atom = String.to_existing_atom(field)
          Ecto.Changeset.add_error(acc, field_atom, text)

        # Handle nested paths by flattening to the first level for now
        %{path: [field | _], text: text} when is_atom(field) ->
          Ecto.Changeset.add_error(acc, field, text)

        %{path: [field | _], text: text} when is_binary(field) ->
          field_atom = String.to_existing_atom(field)
          Ecto.Changeset.add_error(acc, field_atom, text)

        # Handle legacy error format
        {key, {message, _opts}} ->
          Ecto.Changeset.add_error(acc, key, message)

        # Fallback for other error structures
        _ ->
          acc
      end
    end)
    |> Map.put(:action, :validate)
  end

  # Helper function to convert string keys to atom keys
  defp atomize_keys(map) when is_map(map) do
    Map.new(map, fn
      {key, value} when is_binary(key) ->
        try do
          {String.to_existing_atom(key), value}
        rescue
          ArgumentError -> {key, value}
        end

      {key, value} ->
        {key, value}
    end)
  end

  defp atomize_keys(other), do: other
end

# Phoenix.HTML.FormData protocol implementations for form compatibility
# These are only compiled if Phoenix.HTML is available
if Code.ensure_loaded?(Phoenix.HTML.FormData) do
  defimpl Phoenix.HTML.FormData, for: Drops.Operations.Success do
    def to_form(%{params: params, type: :form}, options) do
      # For :form operations, use the validated params as the form data
      # This allows the Success struct to work with Phoenix form helpers
      # Convert atom keys to string keys as required by Phoenix.HTML
      form_data = if is_map(params), do: stringify_keys(params), else: %{}
      create_form_struct(form_data, options, "success")
    end

    def to_form(%{params: params}, options) do
      # For non-form operations, fall back to params
      # Convert atom keys to string keys as required by Phoenix.HTML
      form_data = if is_map(params), do: stringify_keys(params), else: %{}
      create_form_struct(form_data, options, "success")
    end

    def to_form(data, form, field, options) do
      form_data = if is_map(data.params), do: stringify_keys(data.params), else: %{}
      Phoenix.HTML.FormData.to_form(form_data, form, field, options)
    end

    def input_value(%{params: params}, form, field) do
      form_data = if is_map(params), do: stringify_keys(params), else: %{}
      Phoenix.HTML.FormData.input_value(form_data, form, field)
    end

    def input_validations(%{params: _params}, _form, _field) do
      []
    end

    # Helper function to create a proper Phoenix.HTML.Form struct
    defp create_form_struct(form_data, options, default_name) do
      {name, options} = Keyword.pop(options, :as)
      name = to_string(name || default_name)
      id = Keyword.get(options, :id) || name

      %Phoenix.HTML.Form{
        source: form_data,
        impl: __MODULE__,
        id: id,
        name: name,
        data: form_data,
        params: form_data,
        errors: [],
        hidden: [],
        options: options,
        action: nil,
        index: nil
      }
    end

    # Helper function to convert atom keys to string keys
    defp stringify_keys(map) when is_map(map) do
      Map.new(map, fn
        {key, value} when is_atom(key) -> {Atom.to_string(key), value}
        {key, value} -> {key, value}
      end)
    end

    defp stringify_keys(other), do: other
  end

  defimpl Phoenix.HTML.FormData, for: Drops.Operations.Failure do
    def to_form(
          %{operation: operation_module, params: params, result: result, type: :form},
          options
        ) do
      # For :form operations with validation errors, we want to preserve
      # the original params and include error information
      # Convert atom keys to string keys as required by Phoenix.HTML
      form_data = if is_map(params), do: stringify_keys(params), else: %{}

      # If result is an Ecto.Changeset, use it directly for form data
      # as it contains both data and errors
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.to_form(changeset, options)

        # For form operations with Ecto schemas, convert schema validation errors to changeset
        errors when is_list(errors) ->
          if Drops.Operations.Extensions.Ecto.has_ecto_schema?(operation_module) do
            changeset =
              Drops.Operations.Extensions.Ecto.convert_schema_errors_to_changeset(
                operation_module,
                params,
                errors
              )

            Phoenix.HTML.FormData.to_form(changeset, options)
          else
            create_form_struct(form_data, options, "failure")
          end

        # For string errors or other types, create a basic form
        _ ->
          create_form_struct(form_data, options, "failure")
      end
    end

    def to_form(%{params: params, result: result}, options) do
      # For non-form operations, check if result is a changeset
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.to_form(changeset, options)

        _ ->
          form_data = if is_map(params), do: stringify_keys(params), else: %{}
          create_form_struct(form_data, options, "failure")
      end
    end

    def to_form(data, form, field, options) do
      case data.result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.to_form(changeset, form, field, options)

        _ ->
          form_data = if is_map(data.params), do: stringify_keys(data.params), else: %{}
          Phoenix.HTML.FormData.to_form(form_data, form, field, options)
      end
    end

    def input_value(%{params: params, result: result}, form, field) do
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.input_value(changeset, form, field)

        _ ->
          form_data = if is_map(params), do: stringify_keys(params), else: %{}
          Phoenix.HTML.FormData.input_value(form_data, form, field)
      end
    end

    def input_validations(%{params: _params, result: result}, form, field) do
      case result do
        %Ecto.Changeset{} = changeset ->
          Phoenix.HTML.FormData.input_validations(changeset, form, field)

        _ ->
          []
      end
    end

    # Helper function to create a proper Phoenix.HTML.Form struct
    defp create_form_struct(form_data, options, default_name) do
      {name, options} = Keyword.pop(options, :as)
      name = to_string(name || default_name)
      id = Keyword.get(options, :id) || name

      %Phoenix.HTML.Form{
        source: form_data,
        impl: __MODULE__,
        id: id,
        name: name,
        data: form_data,
        params: form_data,
        errors: [],
        hidden: [],
        options: options,
        action: nil,
        index: nil
      }
    end

    # Helper function to convert atom keys to string keys
    defp stringify_keys(map) when is_map(map) do
      Map.new(map, fn
        {key, value} when is_atom(key) -> {Atom.to_string(key), value}
        {key, value} -> {key, value}
      end)
    end

    defp stringify_keys(other), do: other
  end
end
